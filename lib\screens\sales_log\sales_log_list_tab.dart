import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/sales_log_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/category_provider.dart';
import '../../models/sales_log.dart';
import '../../models/sales_log_display_item.dart';
import '../../models/transaction_type.dart';
import 'sales_log_list_business_logic.dart';
import 'sales_log_list_filter_logic.dart';
import 'sales_log_list_ui_components.dart';
import '../../utils/logger_utils.dart';

class SalesLogListTab extends ConsumerStatefulWidget {
  final String selectedSeller;
  final DateTimeRange? selectedDateRange;
  final TransactionType? selectedTransactionType;

  const SalesLogListTab({
    super.key,
    required this.selectedSeller,
    this.selectedDateRange,
    this.selectedTransactionType,
  });

  @override
  ConsumerState<SalesLogListTab> createState() => _SalesLogListTabState();
}

class _SalesLogListTabState extends ConsumerState<SalesLogListTab>
    with RestorationMixin {
  final ScrollController _scrollController = ScrollController();

  @override
  String? get restorationId => 'sales_log_list_tab';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// 스크롤 리스너
  void _onScroll() {
    // 무한 스크롤 제거 - 모든 데이터를 한번에 표시
  }

  /// 판매 기록 삭제 핸들러
  Future<void> _handleDeleteSalesLog(SalesLog salesLog) async {
    await SalesLogListBusinessLogic.deleteSalesLogComplete(
      ref: ref,
      context: context,
      salesLog: salesLog,
    );

    // 삭제 후 데이터 새로고침
    ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
  }

  /// 그룹 판매 기록 삭제 핸들러
  Future<void> _handleDeleteGroup(GroupedSale groupedSale) async {
    await SalesLogListBusinessLogic.deleteGroupSalesLogWithStockRestore(
      ref: ref,
      context: context,
      groupedSale: groupedSale,
    );
    
    // 삭제 후 데이터 새로고침
    ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
  }

  /// 그룹 상세 다이얼로그 표시 핸들러
  void _handleShowGroupDetail(GroupedSale groupedSale) {
    // productCategoryMap 생성 (기존 로직과 동일)
    Map<int, String>? productCategoryMap;
    final productsAsync = ref.read(productNotifierProvider);
    final categoriesAsync = ref.read(categoryNotifierProvider);

    if (!productsAsync.isLoading && !productsAsync.hasError &&
        categoriesAsync.hasValue) {
      final products = productsAsync.products;
      final categories = categoriesAsync.value!;

      // productId -> categoryName 매핑 생성
      productCategoryMap = <int, String>{};
      for (final product in products) {
        try {
          final category = categories.firstWhere(
            (cat) => cat.id == product.categoryId,
          );
          if (product.id != null) {
            productCategoryMap[product.id!] = category.name;
          }
        } catch (e) {
          // 카테고리를 찾을 수 없는 경우 로그만 출력하고 계속 진행
          LoggerUtils.logWarning('카테고리를 찾을 수 없습니다: ${product.categoryId} (상품: ${product.name})', tag: 'SalesLogListTab');
        }
      }
    }

    SalesLogListBusinessLogic.showGroupDetailDialog(
      context: context,
      groupedSale: groupedSale,
      selectedSeller: widget.selectedSeller,
      onItemDelete: (salesLog) => _handleDeleteSalesLog(salesLog),
      productCategoryMap: productCategoryMap,
    );
  }

  /// 데이터 새로고침
  void _refreshData() {
    ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
  }

  @override
  Widget build(BuildContext context) {
    final salesLogState = ref.watch(salesLogNotifierProvider);
    
    // 디버깅 정보 출력
    LoggerUtils.logDebug(
      'SalesLogListTab - 판매 기록 상태: ${salesLogState.salesLogs.length}개',
      tag: 'SalesLogListTab',
    );
    LoggerUtils.logDebug(
      'SalesLogListTab - 로딩 상태: ${salesLogState.isLoading}',
      tag: 'SalesLogListTab',
    );
    LoggerUtils.logDebug(
      'SalesLogListTab - 에러 상태: ${salesLogState.hasError}',
      tag: 'SalesLogListTab',
    );

    if (salesLogState.isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    // 필터링된 판매 기록 목록 생성
    final filteredDisplayItems = _getFilteredDisplayItems(ref, salesLogState.salesLogs);
    
    LoggerUtils.logDebug(
      'SalesLogListTab - 필터링된 판매 기록: ${filteredDisplayItems.length}개',
      tag: 'SalesLogListTab',
    );

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          _refreshData();
        },
        child: filteredDisplayItems.isEmpty
            ? SalesLogListUiComponents.buildEmptyState(
                selectedSeller: widget.selectedSeller,
                selectedTransactionType: widget.selectedTransactionType,
              )
            : RepaintBoundary(
                child: SafeArea(
                  child: CustomScrollView(
                    controller: _scrollController,
                    restorationId: 'sales_log_list_tab_scroll',
                    slivers: [
                      // 판매 기록 리스트 (필터 UI 제거됨)
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final item = filteredDisplayItems[index];
                            return Column(
                              children: [
                                _SalesLogListItem(
                                  item: item,
                                  onDelete: (salesLog) => _handleDeleteSalesLog(salesLog),
                                  onDeleteGroup: (groupedSale) => _handleDeleteGroup(groupedSale),
                                  onShowGroupDetail: (groupedSale) => _handleShowGroupDetail(groupedSale),
                                ),
                                if (index < filteredDisplayItems.length - 1)
                                  const Divider(height: 1),
                              ],
                            );
                          },
                          childCount: filteredDisplayItems.length,
                        ),
                      ),
                      
                      // 에러 메시지
                      if (salesLogState.hasError)
                        SliverToBoxAdapter(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Center(
                              child: Column(
                                children: [
                                  Text(
                                    '데이터 로드 중 오류가 발생했습니다',
                                    style: TextStyle(
                                      color: Colors.red[700],
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  ElevatedButton(
                                    onPressed: _refreshData,
                                    child: const Text('다시 시도'),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  /// 필터링된 판매 기록 목록 반환
  List<SalesLogDisplayItem> _getFilteredDisplayItems(WidgetRef ref, List<SalesLog> salesLogs) {
    try {
      // SalesLog를 SalesLogDisplayItem으로 변환 (SalesLogNotifier의 로직 사용)
      final Map<String?, List<SalesLog>> groupedLogs = {};
      final List<SalesLog> singleLogs = [];

      for (final log in salesLogs) {
        final batchId = log.batchSaleId;
        if (batchId != null && batchId.isNotEmpty) {
          groupedLogs.putIfAbsent(batchId, () => []).add(log);
        } else {
          singleLogs.add(log);
        }
      }

      final List<SalesLogDisplayItem> allDisplayItems = [];

      // 그룹화된 판매 추가
      for (final entry in groupedLogs.entries) {
        if (entry.value.isNotEmpty) {
          allDisplayItems.add(GroupedSale(entry.value, entry.key!));
        }
      }

      // 개별 판매 추가
      for (final log in singleLogs) {
        allDisplayItems.add(SingleItem(log));
      }

      // 타임스탬프 기준 내림차순 정렬
      allDisplayItems.sort(
        (a, b) => b.representativeTimestampMillis().compareTo(
          a.representativeTimestampMillis(),
        ),
      );
      
      // 필터링 적용
      final filteredItems = SalesLogListFilterLogic.getFilteredDisplayItems(
        allDisplayItems: allDisplayItems,
        selectedSeller: widget.selectedSeller,
        selectedTransactionType: widget.selectedTransactionType,
        selectedDateRange: widget.selectedDateRange,
      );
      
      return filteredItems;
    } catch (e) {
      LoggerUtils.logError('판매 기록 필터링 중 오류 발생', tag: 'SalesLogListTab', error: e);
      return [];
    }
  }

  // 필터 섹션 제거됨 - 앱바의 필터 버튼으로 이동
}

/// 개별 판매 기록 아이템 - 깜빡임 방지를 위한 별도 위젯
class _SalesLogListItem extends ConsumerWidget {
  final SalesLogDisplayItem item;
  final Function(SalesLog) onDelete;
  final Function(GroupedSale) onDeleteGroup;
  final Function(GroupedSale) onShowGroupDetail;

  const _SalesLogListItem({
    required this.item,
    required this.onDelete,
    required this.onDeleteGroup,
    required this.onShowGroupDetail,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 상품과 카테고리 정보를 가져와서 productCategoryMap 생성
    final productsAsync = ref.watch(productNotifierProvider);
    final categoriesAsync = ref.watch(categoryNotifierProvider);

    Map<int, String>? productCategoryMap;

    if (!productsAsync.isLoading && !productsAsync.hasError &&
        categoriesAsync.hasValue) {
      final products = productsAsync.products;
      final categories = categoriesAsync.value!;

      // productId -> categoryName 매핑 생성
      productCategoryMap = <int, String>{};
      for (final product in products) {
        try {
          final category = categories.firstWhere(
            (cat) => cat.id == product.categoryId,
          );
          if (product.id != null) {
            productCategoryMap[product.id!] = category.name;
          }
        } catch (e) {
          // 카테고리를 찾을 수 없는 경우 로그만 출력하고 계속 진행
          LoggerUtils.logWarning('카테고리를 찾을 수 없습니다: ${product.categoryId} (상품: ${product.name})', tag: 'SalesLogListTab');
        }
      }
    }

    return RepaintBoundary(
      child: SalesLogListUiComponents.buildSalesLogItem(
        item,
        onDelete: onDelete,
        onDeleteGroup: onDeleteGroup,
        onShowGroupDetail: onShowGroupDetail,
        productCategoryMap: productCategoryMap,
      ),
    );
  }
}
