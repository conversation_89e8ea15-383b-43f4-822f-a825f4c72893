import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:lucide_icons/lucide_icons.dart';

import '../../models/product.dart';
import '../../providers/product_provider.dart';
import '../../providers/category_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/currency_utils.dart';
import '../../utils/device_utils.dart';
import '../../utils/dimens.dart';
import '../../widgets/slider_quantity_widget.dart';


/// 주문 내역 리스트만 표시하는 패널 위젯 (총 합계와 판매 버튼 제외)
class OrderSummaryPanel extends ConsumerStatefulWidget {
  final ValueNotifier<Map<int, int>> productQuantitiesNotifier;
  final ValueNotifier<Map<int, int>> serviceQuantitiesNotifier;
  final Function(Product) onProductIncrease;
  final Function(Product) onProductDecrease;
  final Function(Product) onServiceIncrease;
  final Function(Product) onServiceDecrease;
  final Function(Product)? onProductRemove; // 상품 완전 삭제 콜백 추가
  final Function(Product)? onServiceRemove; // 서비스 완전 삭제 콜백 추가
  final ValueNotifier<bool>? manualDiscountEnabledNotifier; // 수동 할인 활성화 상태
  final ValueNotifier<int>? manualDiscountAmountNotifier; // 수동 할인 금액
  final ValueNotifier<String?>? setDiscountInfoNotifier; // 세트 할인 정보

  const OrderSummaryPanel({
    super.key,
    required this.productQuantitiesNotifier,
    required this.serviceQuantitiesNotifier,
    required this.onProductIncrease,
    required this.onProductDecrease,
    required this.onServiceIncrease,
    required this.onServiceDecrease,
    this.onProductRemove,
    this.onServiceRemove,
    this.manualDiscountEnabledNotifier,
    this.manualDiscountAmountNotifier,
    this.setDiscountInfoNotifier,
  });

  @override
  ConsumerState<OrderSummaryPanel> createState() => _OrderSummaryPanelState();
}

class _OrderSummaryPanelState extends ConsumerState<OrderSummaryPanel> {
  final TextEditingController _manualDiscountController = TextEditingController();

  @override
  void dispose() {
    _manualDiscountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final productState = ref.watch(productNotifierProvider);
    final allProducts = productState.products.where((product) => product.isActive).toList();

    return _buildPanelContent(context, ref, allProducts);
  }

  Widget _buildPanelContent(BuildContext context, WidgetRef ref, List<Product> allProducts) {
    final isTablet = DeviceUtils.isTablet(context);

    return Container(
      clipBehavior: Clip.hardEdge, // 경계 밖으로 나가는 부분 잘라내기
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border.all(
          color: AppColors.onboardingPrimary.withValues(alpha: 0.2),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(0), // 라운딩 제거
        boxShadow: [
          BoxShadow(
            color: AppColors.onboardingPrimary.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min, // 최소 크기로 설정
        children: [
          // 헤더
          _buildHeader(context, isTablet),
          const Divider(height: 1),

          // 주문 내역 리스트 - 스크롤 가능하도록 수정
          Expanded(
            child: _buildOrderList(context, ref, allProducts, isTablet),
          ),

          // 세트 할인 정보 표시 (높이 제한)
          if (widget.setDiscountInfoNotifier != null)
            ConstrainedBox(
              constraints: BoxConstraints(maxHeight: isTablet ? 40 : 32),
              child: _buildSetDiscountInfo(context, isTablet),
            ),

          // 수동 할인 입력 필드 (높이 제한)
          if (widget.manualDiscountEnabledNotifier != null)
            ConstrainedBox(
              constraints: BoxConstraints(maxHeight: isTablet ? 48 : 40),
              child: _buildManualDiscountField(context, isTablet),
            ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isTablet) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? Dimens.space12 : Dimens.space8,
        vertical: isTablet ? Dimens.space2 : 1.0, // 패딩을 최소화
      ),
      child: Row(
        children: [
          Icon(
            LucideIcons.shoppingCart,
            size: isTablet ? 20 : 18,
            color: AppColors.onboardingPrimary,
          ),
          SizedBox(width: isTablet ? Dimens.space8 : Dimens.space6),
          Text(
            '주문 내역',
            style: TextStyle(
              fontSize: isTablet ? 16 : 14,
              fontWeight: FontWeight.w600,
              color: AppColors.onboardingTextPrimary,
            ),
          ),
          const Spacer(), // 버튼들을 오른쪽으로 밀기

          // 수동 할인 ON/OFF 버튼
          if (widget.manualDiscountEnabledNotifier != null)
            ValueListenableBuilder<bool>(
              valueListenable: widget.manualDiscountEnabledNotifier!,
              builder: (context, isEnabled, child) {
                return IconButton(
                  icon: Icon(
                    isEnabled ? Icons.money_off : Icons.money_off_outlined,
                    size: isTablet ? 20 : 18,
                    color: isEnabled ? Colors.red : AppColors.onboardingPrimary,
                  ),
                  onPressed: () {
                    widget.manualDiscountEnabledNotifier!.value = !isEnabled;
                    if (!isEnabled) {
                      // 활성화될 때 금액 초기화
                      widget.manualDiscountAmountNotifier?.value = 0;
                      _manualDiscountController.clear();
                    }
                  },
                  tooltip: isEnabled ? '수동 할인 끄기' : '수동 할인 켜기',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                );
              },
            ),

          SizedBox(width: isTablet ? Dimens.space8 : Dimens.space6),

          // 새로고침 버튼
          IconButton(
            icon: Icon(
              Icons.refresh,
              size: isTablet ? 20 : 18,
              color: AppColors.onboardingPrimary,
            ),
            onPressed: () {
              _refreshOrderData();
            },
            tooltip: '새로고침',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  // 주문 데이터 새로고침
  void _refreshOrderData() {
    // 선택 초기화
    widget.productQuantitiesNotifier.value = {};
    widget.serviceQuantitiesNotifier.value = {};

    // 수동 할인 초기화
    if (widget.manualDiscountEnabledNotifier != null) {
      widget.manualDiscountEnabledNotifier!.value = false;
    }
    if (widget.manualDiscountAmountNotifier != null) {
      widget.manualDiscountAmountNotifier!.value = 0;
    }
    _manualDiscountController.clear();
  }

  // 상품명에 카테고리명을 포함하여 표시명 생성 (판매기록과 동일한 형식)
  String _getProductDisplayName(Product product, WidgetRef ref) {
    try {
      // categoryNotifierProvider로 통일하여 깜빡거림 방지
      final categoriesAsync = ref.read(categoryNotifierProvider);
      if (!categoriesAsync.hasValue) {
        return product.name;
      }

      final categories = categoriesAsync.value!;
      final category = categories.firstWhere(
        (cat) => cat.id == product.categoryId,
      );
      return '[${category.name}] ${product.name}';
    } catch (e) {
      // 카테고리를 찾을 수 없는 경우 원본 상품명 사용
      return product.name;
    }
  }

  Widget _buildOrderList(BuildContext context, WidgetRef ref, List<Product> allProducts, bool isTablet) {
    return ValueListenableBuilder<Map<int, int>>(
      valueListenable: widget.productQuantitiesNotifier,
      builder: (context, productQuantities, child) {
        return ValueListenableBuilder<Map<int, int>>(
          valueListenable: widget.serviceQuantitiesNotifier,
          builder: (context, serviceQuantities, child) {
            // 선택된 상품들
            final selectedProducts = productQuantities.entries
                .where((entry) => entry.value > 0)
                .map((entry) {
                  try {
                    return allProducts.firstWhere((p) => p.id == entry.key);
                  } catch (e) {
                    return null;
                  }
                })
                .where((product) => product != null)
                .cast<Product>()
                .toList();

            // 선택된 서비스들 (서비스는 상품과 동일한 Product 모델 사용)
            final selectedServices = serviceQuantities.entries
                .where((entry) => entry.value > 0)
                .map((entry) {
                  try {
                    return allProducts.firstWhere((p) => p.id == entry.key);
                  } catch (e) {
                    return null;
                  }
                })
                .where((product) => product != null)
                .cast<Product>()
                .toList();

            // 상품과 서비스가 모두 비어있으면 빈 상태 표시
            if (selectedProducts.isEmpty && selectedServices.isEmpty) {
              return SizedBox.expand(
                child: _buildEmptyState(context, isTablet),
              );
            }

            final allItems = <Widget>[];

            // 상품 아이템들 추가
            for (final product in selectedProducts) {
              final quantity = productQuantities[product.id!] ?? 0;
              allItems.add(_buildOrderItem(context, ref, product, quantity, isTablet, false));
            }

            // 서비스 아이템들 추가
            for (final service in selectedServices) {
              final quantity = serviceQuantities[service.id!] ?? 0;
              allItems.add(_buildOrderItem(context, ref, service, quantity, isTablet, true));
            }

            return ClipRect(
              child: SingleChildScrollView(
                clipBehavior: Clip.hardEdge,
                child: Padding(
                  padding: EdgeInsets.all(isTablet ? Dimens.space12 : Dimens.space8),
                  child: Column(
                    children: allItems,
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isTablet) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? Dimens.space24 : Dimens.space16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.shoppingBag,
              size: isTablet ? 48 : 40,
              color: AppColors.onboardingTextSecondary.withValues(alpha: 0.5),
            ),
            SizedBox(height: isTablet ? Dimens.space12 : Dimens.space8),
            Text(
              '선택된 상품이 없습니다',
              textAlign: TextAlign.center, // 중앙 정렬 추가
              style: TextStyle(
                fontSize: isTablet ? 14 : 12,
                color: AppColors.onboardingTextSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(BuildContext context, WidgetRef ref, Product product, int quantity, bool isTablet, bool isService) {
    // 화면 방향 감지
    final orientation = MediaQuery.of(context).orientation;
    final isLandscape = orientation == Orientation.landscape;

    // 방향별 삭제 영역 크기 설정
    final deleteExtentRatio = isLandscape
        ? (isTablet ? 0.12 : 0.10)  // 가로모드: 더 작게
        : (isTablet ? 0.18 : 0.15); // 세로모드: 조금 더 크게

    return Slidable(
      key: ValueKey('${isService ? 'service' : 'product'}_${product.id}'),
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        extentRatio: deleteExtentRatio,
        children: [
          SlidableAction(
            onPressed: (context) {
              // 슬라이드 삭제 실행
              if (isService) {
                widget.onServiceRemove?.call(product);
              } else {
                widget.onProductRemove?.call(product);
              }
            },
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            icon: Icons.delete,
            // 직사각형으로 만들기 (borderRadius 제거)
            flex: 1,
            // 아이콘 중앙 정렬을 위한 설정
            spacing: 0,
          ),
        ],
      ),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isTablet ? Dimens.space12 : Dimens.space10,
          vertical: isTablet ? Dimens.space4 : Dimens.space2,
        ),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(0),
          border: Border.all(
            color: AppColors.neutral20,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // 상품명 (슬라이드 터치 가능 영역)
            Expanded(
              flex: isLandscape ? 4 : 3, // 가로모드에서 이름 영역 더 확보
              child: Text(
                _getProductDisplayName(product, ref),
                style: TextStyle(
                  fontSize: isTablet ? 13 : 11,
                  fontWeight: FontWeight.w500,
                  color: isService ? AppColors.categoryElectricBlue : AppColors.onSurface,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // 수량 조절 슬라이더와 가격 사이 간격을 더 줄여서 슬라이더를 오른쪽으로 이동
            SizedBox(width: isLandscape
                ? (isTablet ? Dimens.space12 : Dimens.space8)  // 가로모드: 더 넓게
                : (isTablet ? Dimens.space12 : Dimens.space8)), // 세로모드: 기본

            // 수량 조절 슬라이더 (터치 차단으로 슬라이드 방지)
            GestureDetector(
              onHorizontalDragStart: (_) {}, // 수평 드래그 차단
              onHorizontalDragUpdate: (_) {}, // 수평 드래그 차단
              child: SliderQuantityWidget(
                quantity: quantity,
                maxQuantity: product.quantity,
                isTablet: isTablet,
                isService: isService,
                onQuantityChanged: (newQuantity) {
                  if (isService) {
                    // 서비스의 경우 직접 수량 설정
                    if (newQuantity > quantity) {
                      for (int i = quantity; i < newQuantity; i++) {
                        widget.onServiceIncrease(product);
                      }
                    } else if (newQuantity < quantity) {
                      for (int i = quantity; i > newQuantity; i--) {
                        widget.onServiceDecrease(product);
                      }
                    }
                  } else {
                    // 상품의 경우 직접 수량 설정
                    if (newQuantity > quantity) {
                      for (int i = quantity; i < newQuantity; i++) {
                        widget.onProductIncrease(product);
                      }
                    } else if (newQuantity < quantity) {
                      for (int i = quantity; i > newQuantity; i--) {
                        widget.onProductDecrease(product);
                      }
                    }
                  }
                },
              ),
            ),

            SizedBox(width: isTablet ? Dimens.space2 : 1), // 수량조절-가격 간격 더 좁게

            // 상품 가격 - 불필요하게 넓은 영역 축소
            GestureDetector(
              onHorizontalDragStart: (_) {}, // 수평 드래그 차단
              onHorizontalDragUpdate: (_) {}, // 수평 드래그 차단
              child: Container(
                width: isTablet ? 70 : 55, // 너비 축소 (기존 120->70, 955570)
                child: Text(
                  isService ? '서비스' : CurrencyUtils.formatCurrency(product.price.round()),
                  style: TextStyle(
                    fontSize: isTablet ? 12 : 10,
                    color: isService ? AppColors.categoryElectricBlue : Colors.black,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.right, // 오른쪽 정렬로 깔끔하게
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 수동 할인 입력 필드 위젯
  Widget _buildManualDiscountField(BuildContext context, bool isTablet) {
    return ValueListenableBuilder<bool>(
      valueListenable: widget.manualDiscountEnabledNotifier!,
      builder: (context, isEnabled, child) {
        if (!isEnabled) return const SizedBox.shrink();

        return Container(
          padding: EdgeInsets.all(isTablet ? Dimens.space12 : Dimens.space10),
          decoration: BoxDecoration(
            color: AppColors.surface,
            border: Border(
              top: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Row(
            children: [
              Text(
                '할인 :',
                style: TextStyle(
                  fontSize: isTablet ? 14 : 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.onboardingTextPrimary,
                ),
              ),
              SizedBox(width: isTablet ? Dimens.space12 : Dimens.space10),
              Expanded(
                child: TextField(
                  controller: _manualDiscountController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  decoration: InputDecoration(
                    hintText: '할인 금액 입력',
                    prefixText: '₩ ',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: AppColors.onboardingPrimary, width: 2),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: isTablet ? Dimens.space12 : Dimens.space10,
                      vertical: isTablet ? Dimens.space10 : Dimens.space8,
                    ),
                    isDense: true,
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  style: TextStyle(
                    fontSize: isTablet ? 14 : 12,
                    fontWeight: FontWeight.w500,
                  ),
                  onChanged: (value) {
                    final amount = int.tryParse(value) ?? 0;
                    widget.manualDiscountAmountNotifier?.value = amount;
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 세트 할인 정보 표시
  Widget _buildSetDiscountInfo(BuildContext context, bool isTablet) {
    return ValueListenableBuilder<String?>(
      valueListenable: widget.setDiscountInfoNotifier!,
      builder: (context, setDiscountInfo, child) {
        if (setDiscountInfo == null || setDiscountInfo.isEmpty) {
          return const SizedBox.shrink();
        }
        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(isTablet ? Dimens.space12 : Dimens.space8),
          margin: EdgeInsets.all(isTablet ? Dimens.space12 : Dimens.space8),
          decoration: BoxDecoration(
            color: AppColors.onboardingPrimary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.local_offer,
                color: AppColors.onboardingPrimary,
                size: isTablet ? 20 : 16,
              ),
              SizedBox(width: isTablet ? Dimens.space8 : Dimens.space4),
              Expanded(
                child: Text(
                  setDiscountInfo,
                  style: TextStyle(
                    fontSize: isTablet ? 14 : 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.onboardingTextPrimary,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
