import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

import '../../models/prepayment_sort_order.dart';
import '../../providers/prepayment_provider.dart';
import '../../providers/prepayment_state.dart';
import '../../models/event_workspace.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../providers/settings_provider.dart';

import '../../utils/orientation_helper.dart';
import '../../utils/error_utils.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../utils/app_colors.dart';
import '../../main.dart';
import '../prepayment/register_prepayment_screen.dart';
import '../product/register_product_screen.dart';
import 'prepayment_tab.dart';



import '../excel/excel_import_screen.dart';
import 'qr_scan_screen.dart';
import '../../widgets/excel_import_mode_dialog.dart';
import '../../providers/realtime_sync_provider.dart';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../providers/nickname_provider.dart';


import '../prepayment/prepayment_virtual_product_management_screen.dart';
import '../prepayment/prepayment_product_link_screen.dart';

class InventoryScreen extends ConsumerStatefulWidget {
  const InventoryScreen({super.key});

  @override
  ConsumerState<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends ConsumerState<InventoryScreen>
    with RestorationMixin {
  static const String _tag = 'PrepaymentScreen';
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // 검색 모드 관련 변수들
  bool _isSearchMode = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  String? get restorationId => 'inventory_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: _tag);

    // 모든 방향 허용 (인벤토리 페이지부터는 가로모드 허용)
    OrientationHelper.enterAllOrientationsMode();

    // 초기 데이터 로드 (한 번만 실행)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
      _initializeRealtimeSync();
    });

    LoggerUtils.methodEnd('initState', tag: _tag);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  /// 완전한 메모리 정리 - 행사 전환 시 크래시 방지
  Future<void> _performCompleteMemoryCleanup() async {
    try {
      LoggerUtils.methodStart('_performCompleteMemoryCleanup', tag: _tag);

      // 이미지 캐시 정리 (await 불필요)
      imageCache.clear();
      imageCache.clearLiveImages();
      
      // 네트워크 이미지 캐시 정리 (cached_network_image 플러그인)
      final cacheManager = DefaultCacheManager();
      await cacheManager.emptyCache();

      // 정적 리소스 정리 (메모리 누수 방지)
      await cleanupAppResources();

      // GC 강제 실행 (개발 모드에서만)
      if (kDebugMode) {
        LoggerUtils.logInfo('메모리 정리 완료 - 강제 GC 요청', tag: _tag);
      }

      LoggerUtils.methodEnd('_performCompleteMemoryCleanup', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('메모리 정리 중 오류: $e', tag: _tag);
    }
  }

  /// 이벤트 변경 후 데이터 새로고침
  Future<void> _refreshDataAfterEventChange() async {
    if (!mounted) return;

    try {
      LoggerUtils.methodStart('_refreshDataAfterEventChange', tag: _tag);

      // 강력한 메모리 정리 - 행사 전환 시 크래시 방지
      await _performCompleteMemoryCleanup();

      // 실시간 동기화가 활성화되어 있으므로 에러 상태만 클리어
      final prepaymentNotifier = ref.read(prepaymentNotifierProvider.notifier);

      // 에러 상태 클리어 (데이터는 실시간 동기화로 자동 갱신됨)
      prepaymentNotifier.clearError();

      LoggerUtils.logInfo('행사 전환 - 메모리 정리 및 상태 클리어 완료', tag: _tag);
      LoggerUtils.methodEnd('_refreshDataAfterEventChange', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('이벤트 변경 후 상태 클리어 실패', tag: _tag, error: e);
    }
  }

  /// 실시간 동기화 초기화
  Future<void> _initializeRealtimeSync() async {
    try {
      LoggerUtils.methodStart('_initializeRealtimeSync', tag: _tag);
      
      // Firebase 인증 확인
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('사용자가 로그인되지 않음 - 실시간 동기화 건너뛰기', tag: _tag);
        return;
      }
      
      // 실시간 동기화 서비스 준비 (RealtimeSyncService v2.0.0)
      final syncService = ref.read(realtimeSyncServiceProvider);
      
      // SyncStateNotifier 초기화
      final syncNotifier = ref.read(syncStateProvider.notifier);
      await syncNotifier.initialize();
      
      // 현재 워크스페이스가 있으면 구독 시작
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace != null) {
        final eventId = currentWorkspace.id;
        await syncService.subscribeToEvent(eventId);
        LoggerUtils.logInfo('행사 $eventId에 대한 실시간 구독 시작됨 (v2.0.0)', tag: _tag);
      }
      
      LoggerUtils.logInfo('실시간 동기화 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('실시간 동기화 초기화 실패', tag: _tag, error: e);
      // 실패해도 앱 동작에는 영향을 주지 않음
    }
  }

  @override
  void dispose() {
    LoggerUtils.methodStart('dispose', tag: _tag);

    _searchController.dispose();

    LoggerUtils.methodEnd('dispose', tag: _tag);
    super.dispose();
  }



  /// 초기 데이터 로드 (한 번만 실행)
  Future<void> _loadInitialData() async {
    if (!mounted) return;

    LoggerUtils.methodStart('_loadInitialData', tag: _tag);

    // 2단계 가드: 현재 행사가 설정되지 않은 경우 데이터 로딩 시도하지 않음
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      LoggerUtils.logWarning('현재 행사가 설정되지 않아 데이터 로딩을 건너뜁니다', tag: _tag);
      LoggerUtils.methodEnd('_loadInitialData', tag: _tag);
      return;
    }

    final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
    if (currentEvent == null) {
      LoggerUtils.logWarning('행사 정보를 불러올 수 없어 데이터 로딩을 건너뜁니다', tag: _tag);
      LoggerUtils.methodEnd('_loadInitialData', tag: _tag);
      return;
    }

    // 초기 데이터 로딩 (실시간 동기화와 별개로 필요)
    LoggerUtils.logInfo('초기 데이터 로딩 시작', tag: _tag);

    try {
      // PrepaymentNotifier에서 데이터 로드
      await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();

      LoggerUtils.logInfo('초기 데이터 로딩 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('초기 데이터 로딩 실패', tag: _tag, error: e);
      // 실패해도 앱은 계속 실행
    }

    LoggerUtils.methodEnd('_loadInitialData', tag: _tag);
  }

  /// 검색 텍스트 변경 처리
  void _onSearchTextChanged(String query) {
    if (!mounted) return;

    LoggerUtils.methodStart('_onSearchTextChanged', tag: _tag);

    ErrorUtils.wrapError(
      context,
      () async {
        if (mounted) {
          await ref
              .read(prepaymentNotifierProvider.notifier)
              .searchPrepayments(query);
        }
      },
      errorMessage: '검색 중 오류가 발생했습니다',
      type: ErrorType.database,
      tag: _tag,
    );

    LoggerUtils.methodEnd('_onSearchTextChanged', tag: _tag);
  }

  /// 검색 모드 토글
  void _toggleSearchMode() {
    if (!mounted) return;

    LoggerUtils.methodStart('_toggleSearchMode', tag: _tag);

    setState(() {
      _isSearchMode = !_isSearchMode;
      if (!_isSearchMode) {
        _searchController.clear();
        if (mounted) {
          ref.read(prepaymentNotifierProvider.notifier).searchPrepayments('');
        }
      }
    });

    LoggerUtils.methodEnd('_toggleSearchMode', tag: _tag);
  }

  @override
  Widget build(BuildContext context) {
    // 현재 워크스페이스 변경 감지 및 데이터 새로고침
    ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) {
      if (previous != null && next != null && previous.id != next.id) {
        LoggerUtils.logInfo('현재 이벤트 변경 감지: ${previous.name} -> ${next.name}', tag: _tag);
        _refreshDataAfterEventChange();
        // Consumer 위젯들이 자동으로 상태 변화를 감지하므로 수동 setState 불필요
      } else if (previous == null && next != null) {
        // 3단계 가드: 현재 행사가 null에서 설정된 경우 (온보딩 완료 후)
        LoggerUtils.logInfo('현재 이벤트 설정됨: ${next.name} - 데이터 새로고침', tag: _tag);
        _refreshDataAfterEventChange();
        // Consumer 위젯들이 자동으로 상태 변화를 감지하므로 수동 setState 불필요
      }
    });

    final workspaceState = ref.watch(unifiedWorkspaceProvider);

    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: _buildToolbarTitle(),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent, // Material 3에서 색상 덮어쓰기 방지
        elevation: 0,
        actions: _buildAppBarActions(),
      ),
      drawer: _buildDrawer(),
      // onDrawerChanged 제거 - 드로어 열 때마다 불필요한 프로필 이미지 체크 방지
      body: Stack(
        children: [
          SafeArea(
            bottom: false, // 하단 SafeArea 제거하여 네비게이션 바가 바닥에 붙도록
            child: const PrepaymentTab(),
          ),
          // 워크스페이스 전환 로딩 오버레이
          if (workspaceState.isLoading) _buildWorkspaceLoadingOverlay(workspaceState),
        ],
      ),
    );
  }

  /// 툴바 타이틀
  Widget _buildToolbarTitle() {
    // 검색 모드인 경우 - 검색창 표시
    if (_isSearchMode) {
      return TextField(
        controller: _searchController,
        autofocus: true,
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', color: Colors.white),
        cursorColor: Colors.white,
        decoration: InputDecoration(
          filled: true,
          fillColor: Colors.black.withValues(alpha: 0.15),
          hintText: '선입금 검색...',
          hintStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', color: Colors.white70),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        ),
        onChanged: _onSearchTextChanged,
      );
    }

    // 일반 모드 - 요일 필터 표시
    return Consumer(
      builder: (context, ref, child) {
        final selectedDay = ref.watch(prepaymentDayOfWeekFilterProvider);
        return GestureDetector(
          onTap: _showDayFilterDialog,
          child: Text(
            selectedDay == 0
                ? '선입금 관리'
                : selectedDay == 8
                    ? '선입금 관리 (없음)'
                    : '선입금 관리 (${_getDayOfWeekName(selectedDay)})',
            style: TextStyle(fontFamily: 'Pretendard',
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        );
      },
    );
  }

  /// 요일 이름 반환 헬퍼 메서드
  String _getDayOfWeekName(int dayOfWeek) {
    switch (dayOfWeek) {
      case 1:
        return '월요일';
      case 2:
        return '화요일';
      case 3:
        return '수요일';
      case 4:
        return '목요일';
      case 5:
        return '금요일';
      case 6:
        return '토요일';
      case 7:
        return '일요일';
      default:
        return '전체';
    }
  }

  /// AppBar 액션들 빌드
  List<Widget> _buildAppBarActions() {
    List<Widget> actions = [];

    // 동기화 상태 아이콘 (모든 탭에 공통으로 표시)
    actions.add(
      const Padding(
        padding: EdgeInsets.only(right: 8.0),
        child: const Text('동기화 상태'),
      ),
    );

    // 선입금 액션들
    // 검색 버튼
    actions.add(
      IconButton(
        icon: Icon(_isSearchMode ? Icons.close : Icons.search),
        onPressed: _toggleSearchMode,
        tooltip: _isSearchMode ? '검색 취소' : '검색',
      ),
    );

    // QR코드 버튼 추가 (돋보기와 정렬 사이)
    actions.add(
      IconButton(
        icon: const Icon(Icons.qr_code_scanner),
        onPressed: _openQrScanScreen,
        tooltip: 'QR코드 스캔',
      ),
    );

    // 정렬 버튼
    if (!_isSearchMode) {
      actions.add(
        IconButton(
          icon: const Icon(Icons.sort),
          onPressed: _showPrepaymentSortDialog,
          tooltip: '정렬',
        ),
      );
    }

    return actions;
  }

  /// 드로어 빌드
  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          Consumer(
            builder: (context, ref, child) {
              final nickname = ref.watch(nicknameProvider);
              return _DrawerProfileHeader(nicknameObj: nickname);
            },
          ),

          const Divider(),

          // [그룹] 상품/선입금 관리 확장형 메뉴
          ExpansionTile(
            leading: const Icon(Icons.shopping_basket),
            title: const Text('상품/선입금 관리'),
            children: [
              ListTile(
                leading: Icon(Icons.add_box, size: 18),
                title: const Text('상품 등록', style: TextStyle(fontSize: 14)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                dense: true,
                onTap: () {
                  Navigator.pop(context);
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const RegisterProductScreen(),
                    ),
                  ).then((result) {
                    if (result == true) {
                      // 상품 등록 후 실시간 동기화로 자동 갱신됨
                      LoggerUtils.logInfo('상품 등록 완료 - 실시간 동기화로 자동 갱신됨', tag: _tag);
                    }
                  });
                },
              ),
              ListTile(
                leading: Icon(Icons.payment, size: 18),
                title: const Text('선입금 등록', style: TextStyle(fontSize: 14)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                dense: true,
                onTap: () {
                  Navigator.pop(context);
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const RegisterPrepaymentScreen(),
                    ),
                  );
                },
              ),
              ListTile(
                leading: Icon(Icons.download, size: 18),
                title: const Text('엑셀로 선입금 등록', style: TextStyle(fontSize: 14)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                dense: true,
                onTap: () async {
                  Navigator.pop(context);
                  const mode = RegistrationMode.prepaymentOnly; // 선입금만 등록
                  final settings = ref.read(settingsNotifierProvider).value;
                  final collectDayOfWeek = settings?.collectDayOfWeekFromExcel ?? false;
                  final dayOfWeekColumnIndex = settings?.excelDayOfWeekColumnIndex ?? -1;
                  final result = await showDialog<ExcelImportDialogResult>(
                    context: context,
                    builder: (context) => ExcelImportModeDialog(
                      initialMode: mode,
                      initialCollectDayOfWeek: collectDayOfWeek,
                      initialDayOfWeekColumnIndex: dayOfWeekColumnIndex,
                    ),
                  );
                  if (result != null) {
                    // 모드 설정은 다이얼로그에서 처리됨
                    ref.read(settingsNotifierProvider.notifier).setCollectDayOfWeekFromExcel(result.collectDayOfWeek);
                    ref.read(settingsNotifierProvider.notifier).setExcelDayOfWeekColumnIndex(result.dayOfWeekColumnIndex);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => ExcelImportScreen(
                          initialMode: result.selectedMode,
                          initialCollectDayOfWeek: result.collectDayOfWeek,
                          initialDayOfWeekColumnIndex: result.dayOfWeekColumnIndex,
                        ),
                      ),
                    );
                  }
                },
              ),
              ListTile(
                leading: Icon(Icons.inventory_2, size: 18),
                title: const Text('선입금 상품 관리', style: TextStyle(fontSize: 14)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                dense: true,
                onTap: () {
                  Navigator.pop(context);
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const PrepaymentVirtualProductManagementScreen(),
                    ),
                  );
                },
              ),
              ListTile(
                leading: Icon(Icons.link, size: 18),
                title: const Text('선입금-상품 연동 관리', style: TextStyle(fontSize: 14)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                dense: true,
                onTap: () {
                  Navigator.pop(context);
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const PrepaymentProductLinkScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
          // [기존 메뉴] 통계, 설정 등은 그대로 유지 (판매 기록은 기록&통계 탭으로 이동)


        ],
      ),
    );
  }



  /// 요일 필터 다이얼로그 표시
  void _showDayFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => Consumer(
          builder: (context, ref, child) {
            final availableDays = ref.watch(prepaymentAvailableDaysOfWeekProvider);
            return AlertDialog(
              title: const Text(
                '요일 선택',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              content: SizedBox(
                width: double.maxFinite,
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    ListTile(
                      title: const Text('전체', style: TextStyle(fontSize: 16)),
                      onTap: () {
                        ref.read(prepaymentDayOfWeekFilterProvider.notifier).state = 0;
                        ref.read(prepaymentNotifierProvider.notifier).filterByDayOfWeek(0);
                        Navigator.pop(context);
                      },
                    ),
                    if (availableDays.isEmpty)
                      const ListTile(
                        title: Text('등록된 요일 없음', style: TextStyle(fontSize: 16, color: Colors.grey)),
                      ),
                    for (int i = 1; i <= 7; i++)
                      if (availableDays.contains(i))
                        ListTile(
                          title: Text(
                            _getDayOfWeekName(i),
                            style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 16),
                          ),
                          onTap: () {
                            ref.read(prepaymentDayOfWeekFilterProvider.notifier).state = i;
                            ref.read(prepaymentNotifierProvider.notifier).filterByDayOfWeek(i);
                            Navigator.pop(context);
                          },
                        ),
                    // '없음' 요일 처리
                    if (availableDays.contains(8))
                      ListTile(
                        title: const Text('없음', style: TextStyle(fontSize: 16)),
                        onTap: () {
                          ref.read(prepaymentDayOfWeekFilterProvider.notifier).state = 8;
                          ref.read(prepaymentNotifierProvider.notifier).filterByDayOfWeek(8);
                          Navigator.pop(context);
                        },
                      ),
                  ],
                ),
              ),
            );
          },
        ),
    );
  }



  /// 선입금 정렬 및 필터 다이얼로그 표시
  void _showPrepaymentSortDialog() {
    final currentOrder = ref.watch(prepaymentSortOrderProvider);
    final currentState = ref.watch(prepaymentNotifierProvider);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
          title: const Text(
            '정렬 및 필터',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView(
              shrinkWrap: true,
              children: [
                // 필터 섹션
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    '필터',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                ...PrepaymentFilter.values.map((filter) => _buildFilterTile(
                  context: context,
                  filter: filter,
                  isSelected: currentState.filter == filter,
                  onTap: () {
                    ref.read(prepaymentNotifierProvider.notifier).setFilter(filter);
                    Navigator.pop(context);
                  },
                )),

                const Divider(height: 32),

                // 정렬 섹션
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    '정렬',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                _buildSortTile(
                  context: context,
                  title: '이름순',
                  isSelected: currentOrder.isNameSort,
                  isAscending: currentOrder.isNameSort ? currentOrder.isAscending : true,
                  onTap: () {
                    final newOrder = currentOrder.isNameSort
                        ? currentOrder.toggleNameSort()
                        : PrepaymentSortOrder.buyerNameAsc;
                    ref.read(prepaymentSortOrderProvider.notifier).state = newOrder;
                    ref.read(prepaymentNotifierProvider.notifier).loadPrepaymentsSorted(newOrder);
                    Navigator.pop(context);
                  },
                ),
                _buildSortTile(
                  context: context,
                  title: '작성 날짜순',
                  isSelected: currentOrder == PrepaymentSortOrder.writtenDateAsc || currentOrder == PrepaymentSortOrder.writtenDateDesc,
                  isAscending: currentOrder == PrepaymentSortOrder.writtenDateAsc,
                  onTap: () {
                    final newOrder = currentOrder == PrepaymentSortOrder.writtenDateAsc
                        ? PrepaymentSortOrder.writtenDateDesc
                        : PrepaymentSortOrder.writtenDateAsc;
                    ref.read(prepaymentSortOrderProvider.notifier).state = newOrder;
                    ref.read(prepaymentNotifierProvider.notifier).loadPrepaymentsSorted(newOrder);
                    Navigator.pop(context);
                  },
                ),
                _buildSortTile(
                  context: context,
                  title: '금액순',
                  isSelected: currentOrder == PrepaymentSortOrder.amountAsc || currentOrder == PrepaymentSortOrder.amountDesc,
                  isAscending: currentOrder == PrepaymentSortOrder.amountAsc,
                  onTap: () {
                    final newOrder = currentOrder == PrepaymentSortOrder.amountAsc
                        ? PrepaymentSortOrder.amountDesc
                        : PrepaymentSortOrder.amountAsc;
                    ref.read(prepaymentSortOrderProvider.notifier).state = newOrder;
                    ref.read(prepaymentNotifierProvider.notifier).loadPrepaymentsSorted(newOrder);
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        ),
    );
  }

  Widget _buildSortTile({
    required BuildContext context,
    required String title,
    required bool isSelected,
    required bool isAscending,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: isSelected ? Theme.of(context).primaryColor.withValues(alpha: 0.15) : Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? Theme.of(context).primaryColor : null,
          ),
        ),
        trailing: isSelected
            ? Icon(
                isAscending ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                color: Theme.of(context).primaryColor,
              )
            : null,
        onTap: onTap,
      ),
    );
  }

  Widget _buildFilterTile({
    required BuildContext context,
    required PrepaymentFilter filter,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: isSelected ? Theme.of(context).primaryColor.withValues(alpha: 0.15) : Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        title: Text(
          filter.displayName,
          style: TextStyle(
            fontSize: 16,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? Theme.of(context).primaryColor : null,
          ),
        ),
        trailing: isSelected
            ? Icon(
                Icons.check,
                color: Theme.of(context).primaryColor,
              )
            : null,
        onTap: onTap,
      ),
    );
  }

  void _openQrScanScreen() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const QrScanScreen(),
      ),
    );
    if (result is String && result.isNotEmpty) {
      // QR코드 결과가 URL이면 웹사이트로 이동
      final uri = Uri.tryParse(result);
      if (uri != null && (uri.isScheme('http') || uri.isScheme('https'))) {
        try {
          final canLaunch = await canLaunchUrl(uri);
          if (canLaunch) {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
          } else {
            // canLaunchUrl이 false를 반환해도 실제로는 열릴 수 있으므로 시도
            await launchUrl(uri, mode: LaunchMode.externalApplication);
          }
        } catch (e) {
          ToastUtils.showError(context, 'URL을 열 수 없습니다: $result');
        }
      } else {
        ToastUtils.showInfo(context, 'QR코드 결과: $result');
      }
    }
  }

  /// 워크스페이스 전환 로딩 오버레이
  Widget _buildWorkspaceLoadingOverlay(UnifiedWorkspaceState workspaceState) {
    return Container(
      color: Colors.black.withValues(alpha: 0.5),
      child: Center(
        child: Card(
          margin: const EdgeInsets.all(32),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  workspaceState.loadingMessage ?? '행사 전환 중...',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (workspaceState.errorMessage != null) ...[
                  const SizedBox(height: 12),
                  Text(
                    workspaceState.errorMessage!,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// DrawerHeader 대체: 로그인한 사용자의 닉네임을 Firestore에서 직접 불러와 표시
class _UserNicknameHeader extends StatefulWidget {
  const _UserNicknameHeader();

  @override
  State<_UserNicknameHeader> createState() => _UserNicknameHeaderState();
}

class _UserNicknameHeaderState extends State<_UserNicknameHeader> {
  String? _nickname;
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadNickname();
  }

  Future<void> _loadNickname() async {
    setState(() { _loading = true; });
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
        if (doc.exists && doc.data() != null && doc.data()!['nickname'] != null) {
          _nickname = doc.data()!['nickname'] as String;
        }
      }
    } catch (_) {}
    setState(() { _loading = false; });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(color: Colors.blue),
      padding: const EdgeInsets.only(top: 32, left: 16, right: 16, bottom: 16),
      child: Row(
        children: [
          CircleAvatar(
            radius: 32,
            backgroundColor: Colors.white,
            child: Icon(Icons.person, size: 40, color: Colors.blue),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _loading
                    ? const SizedBox(height: 24, width: 80, child: LinearProgressIndicator())
                    : Text(
                        _nickname ?? '닉네임 없음',
                        style: TextStyle(fontFamily: 'Pretendard', 
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _DrawerProfileHeader extends StatelessWidget {
  final dynamic nicknameObj; // Nickname? 타입으로 받음
  const _DrawerProfileHeader({this.nicknameObj});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: Theme.of(context).colorScheme.primary),
      padding: const EdgeInsets.only(top: 32, left: 16, right: 16, bottom: 16),
      child: Row(
        children: [
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  nicknameObj?.name ?? '닉네임 없음',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}






