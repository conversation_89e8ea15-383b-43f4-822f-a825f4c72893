import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/prepayment_provider.dart';
import '../../providers/prepayment_virtual_product_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../models/prepayment.dart';

import '../../utils/logger_utils.dart';
import '../../utils/error_utils.dart';
import '../../utils/currency_utils.dart';
import '../../utils/app_colors.dart';
import '../prepayment/register_prepayment_screen.dart';
import '../../utils/date_utils.dart' as app_date_utils;

class PrepaymentTab extends ConsumerStatefulWidget {
  const PrepaymentTab({super.key});

  @override
  ConsumerState<PrepaymentTab> createState() => _PrepaymentTabState();
}

class _PrepaymentTabState extends ConsumerState<PrepaymentTab>
    with RestorationMixin {
  static const String _tag = 'PrepaymentTab';

  @override
  String? get restorationId => 'prepayment_tab';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 진입 시 항상 새로고침 (빌드 이후 안전하게)
    Future.microtask(() async {
      try {
        // 순서대로 로딩하여 충돌 방지
        await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments(showLoading: false);
        await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
      } catch (e) {
        LoggerUtils.logError('PrepaymentTab 데이터 로딩 중 오류: $e', tag: _tag);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    LoggerUtils.methodStart('build', tag: _tag);

    return Consumer(
      builder: (context, ref, child) {
        // 4단계 가드: 현재 행사가 null인지 먼저 확인
        final currentWorkspace = ref.watch(currentWorkspaceProvider);
        if (currentWorkspace == null) {
          return const _NoEventSelectedWidget();
        }

        final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
        if (currentEvent == null) {
          return const _NoEventSelectedWidget();
        }

        final prepaymentState = ref.watch(prepaymentNotifierProvider);
        final errorMessage = ref.watch(prepaymentErrorMessageProvider);
        final isLoading = prepaymentState.isLoading;

        if (isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  '데이터를 불러오는 중 오류가 발생했습니다\n$errorMessage',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    if (mounted) {
                      ErrorUtils.wrapError(
                        context,
                        () async {
                          if (mounted) {
                            await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments(showLoading: false);
                            await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
                          }
                        },
                        errorMessage: '데이터를 다시 불러오는 중 오류가 발생했습니다',
                        type: ErrorType.database,
                        tag: _tag,
                      );
                    }
                  },
                  child: const Text('다시 시도'),
                ),
              ],
            ),
          );
        }

        // 요일/검색/정렬 등 모든 필터가 provider에서 처리됨
        final filteredPrepayments = prepaymentState.filteredPrepayments;
        LoggerUtils.logDebug('PrepaymentTab - 필터링된 선입금: ${filteredPrepayments.length}개', tag: _tag);

        if (filteredPrepayments.isEmpty) {
          return const Center(
            child: Text(
              '등록된 선입금이 없습니다\n인벤토리 메뉴에서 선입금을 등록해주세요',
              textAlign: TextAlign.center,
            ),
          );
        }

        return RepaintBoundary(
          child: _PrepaymentList(prepayments: filteredPrepayments),
        );
      },
    );
  }

  /// 필터링된 선입금 목록 반환
  // List<Prepayment> _getFilteredPrepayments(WidgetRef ref, List<Prepayment> prepayments) {
  //   var filtered = List<Prepayment>.from(prepayments);
    
  //   // 검색어 필터링
  //   final searchQuery = ref.read(prepaymentSearchQueryProvider);
  //   if (searchQuery.isNotEmpty) {
  //     filtered = filtered.where((prepayment) =>
  //       prepayment.buyerName.toLowerCase().contains(searchQuery.toLowerCase()) ||
  //       prepayment.buyerContact.contains(searchQuery) ||
  //       prepayment.email.toLowerCase().contains(searchQuery.toLowerCase()) ||
  //       prepayment.productNameList.toLowerCase().contains(searchQuery.toLowerCase())
  //     ).toList();
  //   }
    
  //   // 정렬
  //   final sortOrder = ref.read(prepaymentSortOrderProvider);
  //   switch (sortOrder) {
  //     case PrepaymentSortOrder.writtenDateDesc:
  //       filtered.sort((a, b) => b.registrationDate.compareTo(a.registrationDate));
  //       break;
  //     case PrepaymentSortOrder.writtenDateAsc:
  //       filtered.sort((a, b) => a.registrationDate.compareTo(b.registrationDate));
  //       break;
  //     case PrepaymentSortOrder.amountDesc:
  //       filtered.sort((a, b) => b.amount.compareTo(a.amount));
  //       break;
  //     case PrepaymentSortOrder.amountAsc:
  //       filtered.sort((a, b) => a.amount.compareTo(b.amount));
  //       break;
  //     case PrepaymentSortOrder.buyerNameAsc:
  //       filtered.sort((a, b) => a.buyerName.compareTo(b.buyerName));
  //       break;
  //     case PrepaymentSortOrder.buyerNameDesc:
  //       filtered.sort((a, b) => b.buyerName.compareTo(a.buyerName));
  //       break;
  //   }
    
  //   return filtered;
  // }

  // FloatingActionButton 제거됨 - 인벤토리 화면의 메뉴에서 선입금 등록 가능
}

/// 선입금 목록 위젯 - 깜빡임 방지를 위한 별도 위젯
class _PrepaymentList extends StatelessWidget {
  final List<Prepayment> prepayments;

  const _PrepaymentList({required this.prepayments});

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: ListView.builder(
        itemCount: prepayments.length,
        itemExtent: 80, // 고정 높이로 성능 최적화
        cacheExtent: 500.0, // 적절한 캐시 범위 (1000.0에서 조정)
        addRepaintBoundaries: true, // RepaintBoundary 활성화
        addAutomaticKeepAlives: false, // 메모리 최적화
        clipBehavior: Clip.none, // 불필요한 클리핑 제거
        itemBuilder: (context, index) {
          final prepayment = prepayments[index];
          return RepaintBoundary(
            key: ValueKey('prepayment_${prepayment.id}'), // 위젯 재사용을 위한 키
            child: _PrepaymentListItem(prepayment: prepayment),
          );
        },
        restorationId: 'prepayment_tab_scroll',
      ),
    );
  }
}

/// 개별 선입금 아이템 - 깜빡임 방지를 위한 별도 위젯
class _PrepaymentListItem extends ConsumerWidget {
  final Prepayment prepayment;

  const _PrepaymentListItem({required this.prepayment});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return RepaintBoundary(
      child: Stack(
        children: [
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: ListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              title: Row(
                children: [
                  Text(
                    prepayment.buyerName,
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    CurrencyUtils.formatCurrencyWithWon(prepayment.amount),
                    style: TextStyle(fontFamily: 'Pretendard', 
                      color: Colors.black87,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
              trailing: _ReceiveToggleButton(prepayment: prepayment),
              onTap: () => _showPrepaymentDetailScreen(context, prepayment),
            ),
          ),
          if (prepayment.isReceived)
            Positioned.fill(
              child: IgnorePointer(
                ignoring: true,
                child: Container(
                  color: Colors.grey.withValues(alpha: 0.2),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showPrepaymentDetailScreen(BuildContext context, Prepayment prepayment) async {
    LoggerUtils.methodStart('_showPrepaymentDetailScreen', tag: 'PrepaymentTab');

    showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, _) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          backgroundColor: AppColors.surface,
          elevation: 8,
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500, maxHeight: 700),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 모던 헤더
                Container(
                  padding: const EdgeInsets.all(24.0),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.primarySeed,
                        AppColors.primarySeed.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.onPrimary.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.account_balance_wallet,
                          color: AppColors.onPrimary,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${prepayment.buyerName}님의 선입금',
                              style: TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: AppColors.onPrimary,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              CurrencyUtils.formatCurrency(prepayment.amount),
                              style: TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppColors.onPrimary.withValues(alpha: 0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Icon(Icons.close, color: AppColors.onPrimary),
                        style: IconButton.styleFrom(
                          backgroundColor: AppColors.onPrimary.withValues(alpha: 0.2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // 모던 컨텐츠
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                // 구매자 정보
                _buildDetailSection('구매자 정보', [
                  _buildDetailRow('구매자명', prepayment.buyerName),
                  _buildDetailRow('연락처', prepayment.buyerContact),
                  _buildDetailRow('이메일', prepayment.email),
                  if (prepayment.twitterAccount?.isNotEmpty == true)
                    _buildDetailRow('트위터', prepayment.twitterAccount!),
                ]),
                
                const SizedBox(height: 16),
                
                // 결제 정보
                _buildDetailSection('결제 정보', [
                  _buildDetailRow('결제 금액', CurrencyUtils.formatCurrency(prepayment.amount)),
                  _buildDetailRow('입금 계좌', prepayment.bankName),
                ]),
                
                const SizedBox(height: 16),
                
                // 수령 정보
                _buildDetailSection('수령 정보', [
                  _buildDetailRow('수령 요일', prepayment.pickupDays.join(', ')),
                  _buildProductListRow('구매 상품', prepayment.productNameList.split(', ').map((product) => product.trim()).toList()),
                  if (prepayment.memo?.isNotEmpty == true)
                    _buildDetailRow('메모', prepayment.memo!),
                ]),
                
                const SizedBox(height: 16),
                
                // 등록 정보
                _buildDetailSection('등록 정보', [
                  _buildDetailRow(
                    '작성 날짜',
                    app_date_utils.DateUtils.formatDotDate(prepayment.registrationDate)
                  ),
                  _buildDetailRow('수령 상태', prepayment.isReceived ? '수령 완료' : '미수령'),
                  if (prepayment.orderNumber?.isNotEmpty == true)
                    _buildDetailRow('주문번호', prepayment.orderNumber!),
                ]),
                        ],
                      ),
                    ),
                  ),
                ),
                // 모던 하단 버튼
                Container(
                  padding: const EdgeInsets.all(20.0),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                    border: Border(
                      top: BorderSide(
                        color: AppColors.neutral20,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                          backgroundColor: AppColors.surfaceVariant,
                          foregroundColor: AppColors.onSurfaceVariant,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          '닫기',
                          style: TextStyle(
                            fontFamily: 'Pretendard',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          _editPrepayment(context, prepayment);
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                          backgroundColor: AppColors.primarySeed,
                          foregroundColor: AppColors.onPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          '수정',
                          style: TextStyle(
                            fontFamily: 'Pretendard',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      TextButton(
                        onPressed: () async {
                          Navigator.of(context).pop();
                          await ref.read(prepaymentNotifierProvider.notifier).deletePrepayment(prepayment.id);
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                          backgroundColor: AppColors.error.withValues(alpha: 0.1),
                          foregroundColor: AppColors.error,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          '삭제',
                          style: TextStyle(
                            fontFamily: 'Pretendard',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    LoggerUtils.methodEnd('_showPrepaymentDetailScreen', tag: 'PrepaymentTab');
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: 0.5,
        shadowColor: AppColors.elevation1,
        surfaceTintColor: AppColors.surfaceTint,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: AppColors.neutral20,
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 4,
                    height: 20,
                    decoration: BoxDecoration(
                      color: AppColors.primarySeed,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.onSurface,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              ...children,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 90,
            child: Text(
              '$label:',
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductListRow(String label, List<String> products) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 90,
            child: Text(
              '$label:',
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Wrap(
              spacing: 8,
              runSpacing: 4,
              children: products.map((product) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: AppColors.neutral30.withValues(alpha: 0.5),
                    width: 0.5,
                  ),
                ),
                child: Text(
                  product,
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              )).toList(),
            ),
          ),
        ],
      ),
    );
  }

  void _editPrepayment(BuildContext context, Prepayment prepayment) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RegisterPrepaymentScreen(prepaymentId: prepayment.id),
      ),
    );
    if (result == true) {
      // 선입금 수정 후 데이터 새로고침
      final ref = ProviderScope.containerOf(context);
      await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments(showLoading: false);
      await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
    }
  }
}

class _ReceiveToggleButton extends ConsumerWidget {
  final Prepayment prepayment;
  const _ReceiveToggleButton({required this.prepayment});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextButton(
      onPressed: () async {
        await ref.read(prepaymentNotifierProvider.notifier)
            .updateReceiveStatus(context, prepayment.id, !prepayment.isReceived);
      },
      style: TextButton.styleFrom(
        backgroundColor: prepayment.isReceived ? Colors.grey.shade300 : Colors.green,
        foregroundColor: prepayment.isReceived ? Colors.black87 : Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
      ),
      child: Text(
        prepayment.isReceived ? '수령' : '미수령',
        style: TextStyle(fontFamily: 'Pretendard', 
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

/// 행사가 선택되지 않았을 때 표시되는 안내 위젯
class _NoEventSelectedWidget extends StatelessWidget {
  const _NoEventSelectedWidget();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.event_busy, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            '행사를 선택해주세요',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            '왼쪽 상단 메뉴에서 행사를 선택하거나\n새 행사를 생성할 수 있습니다.',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
            icon: const Icon(Icons.menu),
            label: const Text('메뉴 열기'),
          ),
        ],
      ),
    );
  }
}



