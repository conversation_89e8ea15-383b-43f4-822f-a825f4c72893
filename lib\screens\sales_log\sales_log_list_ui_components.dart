import 'package:flutter/material.dart';
import '../../models/sales_log.dart';
import '../../models/sales_log_display_item.dart';
import '../../models/transaction_type.dart';
import '../../models/category.dart' as model_category;
import '../../utils/currency_utils.dart';
import '../../utils/date_utils.dart' as app_date_utils;
import '../../widgets/confirmation_dialog.dart';
import '../../utils/app_colors.dart';

/// 판매 기록 목록의 UI 컴포넌트들을 담당하는 클래스
///
/// 주요 기능:
/// - 개별 판매 기록 아이템 위젯
/// - 그룹 판매 기록 아이템 위젯
/// - 빈 상태 위젯
/// - 다이얼로그 위젯들
class SalesLogListUiComponents {
  /// 빈 상태 위젯
  static Widget buildEmptyState({
    required String selectedSeller,
    required TransactionType? selectedTransactionType,
  }) {
    String message = '판매 기록이 없습니다.';

    if (selectedSeller != '전체 판매자') {
      message = '$selectedSeller의 판매 기록이 없습니다.';
    }

    if (selectedTransactionType != null) {
      message = '${selectedTransactionType.displayName} 기록이 없습니다.';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.receipt_long_outlined, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(fontFamily: 'Pretendard', fontSize: 18, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 표시 아이템 위젯 (개별/그룹 구분)
  static Widget buildSalesLogItem(
    SalesLogDisplayItem displayItem, {
    required Function(SalesLog) onDelete,
    required Function(GroupedSale) onDeleteGroup,
    required Function(GroupedSale) onShowGroupDetail,
    List<model_category.Category>? categories,
    Map<int, String>? productCategoryMap, // productId -> categoryName 매핑
  }) {
    if (displayItem is SingleItem) {
      return buildSingleSalesLogItem(
        displayItem.salesLog,
        onDelete: onDelete,
        categories: categories,
        productCategoryMap: productCategoryMap,
      );
    } else if (displayItem is GroupedSale) {
      return buildGroupedSalesLogItem(
        displayItem,
        onDelete: onDeleteGroup,
        onShowDetail: onShowGroupDetail,
        categories: categories,
        productCategoryMap: productCategoryMap,
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  /// 개별 판매 기록 아이템 위젯 (모던 카드 디자인)
  static Widget buildSingleSalesLogItem(
    SalesLog salesLog, {
    required Function(SalesLog) onDelete,
    List<model_category.Category>? categories,
    Map<int, String>? productCategoryMap, // productId -> categoryName 매핑
  }) {
    final date = DateTime.fromMillisecondsSinceEpoch(salesLog.saleTimestamp);
    final dateStr = app_date_utils.DateUtils.formatKorDateTime(date);

    // 카테고리명과 상품명 조합
    String displayName = salesLog.productName;
    if (productCategoryMap != null && salesLog.productId != null) {
      final categoryName = productCategoryMap[salesLog.productId];
      if (categoryName != null) {
        displayName = '[$categoryName]${salesLog.productName}';
      }
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      child: Card(
        elevation: 1,
        shadowColor: AppColors.elevation1,
        surfaceTintColor: AppColors.surfaceTint,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          // onTap: () => _showSingleSaleDetail(salesLog), // 클릭 시 다이얼로그 표시 (추후 구현)
          onLongPress: () => onDelete(salesLog),
          borderRadius: BorderRadius.circular(12),
          splashColor: AppColors.primarySeed.withValues(alpha: 0.08),
          highlightColor: AppColors.primarySeed.withValues(alpha: 0.04),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 날짜/시간 정보 (개선된 레이아웃)
                Container(
                  width: 80,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        dateStr.split(' ')[1], // 시간 부분
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primarySeed,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        dateStr.split(' ')[0], // 날짜 부분
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 12,
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // 상품 정보 (확장 가능)
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 상품명 (카테고리명 포함)
                      Text(
                        displayName,
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: AppColors.onSurface,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 4),

                      // 판매자 정보와 결제유형 (한 줄로)
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              '판매자: ${salesLog.sellerName ?? '알 수 없음'}',
                              style: TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 13,
                                color: AppColors.onSurfaceVariant,
                              ),
                            ),
                          ),
                          if (salesLog.paymentMethod != null && salesLog.paymentMethod!.isNotEmpty)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                              decoration: BoxDecoration(
                                color: AppColors.surfaceVariant,
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(
                                  color: AppColors.neutral30.withValues(alpha: 0.5),
                                  width: 0.5,
                                ),
                              ),
                              child: Text(
                                salesLog.paymentMethod!,
                                style: TextStyle(
                                  fontFamily: 'Pretendard',
                                  fontSize: 11,
                                  color: AppColors.onSurfaceVariant,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 12),

                // 우측 정보 (수량, 금액)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // 수량 정보
                    Text(
                      '${salesLog.soldQuantity}개',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: AppColors.onSurfaceVariant,
                      ),
                    ),

                    const SizedBox(height: 4),

                    // 총 금액 (세트 할인이 있는 경우 아이콘 표시)
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (salesLog.setDiscountAmount > 0)
                          Padding(
                            padding: const EdgeInsets.only(right: 4),
                            child: Icon(
                              Icons.local_offer,
                              size: 14,
                              color: AppColors.success,
                            ),
                          ),
                        Text(
                          CurrencyUtils.formatCurrency(salesLog.totalAmount),
                          style: TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                            color: AppColors.success,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 그룹 판매 기록 아이템 위젯 (모던 카드 디자인)
  static Widget buildGroupedSalesLogItem(
    GroupedSale groupedSale, {
    required Function(GroupedSale) onDelete,
    required Function(GroupedSale) onShowDetail,
    List<model_category.Category>? categories,
    Map<int, String>? productCategoryMap, // productId -> categoryName 매핑
  }) {
    final date = DateTime.fromMillisecondsSinceEpoch(
      groupedSale.representativeTimestampMillis(),
    );
    final dateStr = app_date_utils.DateUtils.formatKorDateTime(date);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      child: Card(
        elevation: 1,
        shadowColor: AppColors.elevation1,
        surfaceTintColor: AppColors.surfaceTint,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: () => onShowDetail(groupedSale),
          onLongPress: () => onDelete(groupedSale),
          borderRadius: BorderRadius.circular(12),
          splashColor: AppColors.primarySeed.withValues(alpha: 0.08),
          highlightColor: AppColors.primarySeed.withValues(alpha: 0.04),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 다중판매 아이콘
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppColors.primarySeed.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.group,
                    size: 18,
                    color: AppColors.primarySeed,
                  ),
                ),

                const SizedBox(width: 12),

                // 날짜/시간 정보 (개선된 레이아웃)
                Container(
                  width: 80,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        dateStr.split(' ')[1], // 시간 부분
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primarySeed,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        dateStr.split(' ')[0], // 날짜 부분
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 12,
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // 상품 정보 (확장 가능)
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 다중판매 배지와 그룹 제목
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppColors.primarySeed,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '다중',
                              style: TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 10,
                                color: AppColors.onPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _buildGroupTitleWithCategory(groupedSale, productCategoryMap),
                              style: TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: AppColors.onSurface,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 4),

                      // 판매자 정보와 결제유형 (한 줄로)
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              '판매자: ${groupedSale.sellerDisplayText}',
                              style: TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 13,
                                color: AppColors.onSurfaceVariant,
                              ),
                            ),
                          ),
                          // 그룹의 결제유형들 표시
                          if (_getGroupPaymentMethods(groupedSale).isNotEmpty)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                              decoration: BoxDecoration(
                                color: AppColors.surfaceVariant,
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(
                                  color: AppColors.neutral30.withValues(alpha: 0.5),
                                  width: 0.5,
                                ),
                              ),
                              child: Text(
                                _getGroupPaymentMethods(groupedSale),
                                style: TextStyle(
                                  fontFamily: 'Pretendard',
                                  fontSize: 11,
                                  color: AppColors.onSurfaceVariant,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // 우측 정보 (수량, 금액)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // 총 수량 정보
                    Text(
                      '총 ${groupedSale.totalQuantity}개',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: AppColors.onSurfaceVariant,
                      ),
                    ),

                    const SizedBox(height: 4),

                    // 총 금액 (세트 할인이 있는 경우 아이콘 표시)
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (groupedSale.hasSetDiscount)
                          Padding(
                            padding: const EdgeInsets.only(right: 4),
                            child: Icon(
                              Icons.local_offer,
                              size: 14,
                              color: AppColors.success,
                            ),
                          ),
                        Text(
                          CurrencyUtils.formatCurrency(groupedSale.totalGroupAmount),
                          style: TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                            color: AppColors.success,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  /// 삭제 확인 다이얼로그
  static Future<bool> showDeleteConfirmDialog(
    BuildContext context,
    SalesLog salesLog,
  ) async {
    return await ConfirmationDialog.showDelete(
      context: context,
      title: '판매 기록 삭제',
      message: '\'${salesLog.productName}\' (판매자: ${salesLog.sellerName ?? '알 수 없음'}) 판매 기록을 삭제하시겠습니까? ${salesLog.transactionType == TransactionType.sale && salesLog.productId != null ? "삭제 시 재고가 복구됩니다." : "삭제 시 재고가 복구됩니다."}',
    ) ?? false;
  }

  /// 그룹 제목에 카테고리명을 포함하여 생성
  static String _buildGroupTitleWithCategory(
    GroupedSale groupedSale,
    Map<int, String>? productCategoryMap,
  ) {
    if (groupedSale.items.isEmpty) return '';

    // 첫 번째 아이템의 상품명에 카테고리명 추가
    final firstItem = groupedSale.items.first;
    String displayName = firstItem.productName;

    // 카테고리명 추가
    if (productCategoryMap != null && firstItem.productId != null) {
      final categoryName = productCategoryMap[firstItem.productId];
      if (categoryName != null) {
        displayName = '[$categoryName]${firstItem.productName}';
      }
    }

    // 8글자 제한 적용 (카테고리명 포함)
    if (displayName.length <= 8) {
      // 그대로 사용
    } else {
      displayName = '${displayName.substring(0, 8)}..';
    }

    // 여러 종류인 경우 "외 N종류" 추가
    final itemCount = groupedSale.items.length;
    if (itemCount == 1) {
      return displayName;
    } else {
      return '$displayName 외 ${itemCount - 1}종류';
    }
  }

  /// 그룹 삭제 다이얼로그 설명 텍스트 생성
  static String _buildDeleteGroupDescription(
    GroupedSale groupedSale,
    int totalQuantity,
    String totalAmountFormatted,
  ) {
    final itemCount = groupedSale.items.length;
    final sellerText = groupedSale.sellerDisplayText;

    if (itemCount == 1) {
      return '\'${groupedSale.representativeProductNameForDisplay}\' (판매자: $sellerText, 총 $totalQuantity개, $totalAmountFormatted)의 판매 기록을 삭제하시겠습니까? 삭제 시 재고가 복구됩니다.';
    } else {
      return '\'${groupedSale.representativeProductNameForDisplay}\' 외 ${itemCount - 1}종류 (판매자: $sellerText, 총 $totalQuantity개, $totalAmountFormatted)의 묶음 판매 기록을 삭제하시겠습니까? 삭제 시 관련된 모든 판매 기록의 재고가 복구됩니다.';
    }
  }

  /// 그룹 삭제 확인 다이얼로그
  static Future<bool> showDeleteGroupConfirmDialog(
    BuildContext context,
    GroupedSale groupedSale,
  ) async {
    final totalQuantity = groupedSale.totalQuantity;
    final totalAmountFormatted = CurrencyUtils.formatCurrency(
      groupedSale.totalGroupAmount,
    );

    return await ConfirmationDialog.showDelete(
      context: context,
      title: '묶음 판매 기록 삭제',
      message: _buildDeleteGroupDescription(groupedSale, totalQuantity, totalAmountFormatted),
      confirmLabel: '삭제',
      cancelLabel: '취소',
    ) ?? false;
  }

  /// 그룹 판매의 결제유형들을 문자열로 반환
  static String _getGroupPaymentMethods(GroupedSale groupedSale) {
    final paymentMethods = <String>{};
    for (final item in groupedSale.items) {
      if (item.paymentMethod != null && item.paymentMethod!.isNotEmpty) {
        paymentMethods.add(item.paymentMethod!);
      }
    }

    if (paymentMethods.isEmpty) return '';

    final methods = paymentMethods.toList()..sort();
    if (methods.length == 1) {
      return methods.first;
    } else {
      return '${methods.first} 외 ${methods.length - 1}개';
    }
  }
}


